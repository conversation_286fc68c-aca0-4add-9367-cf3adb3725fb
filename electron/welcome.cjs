const { BrowserWindow, app } = require('electron');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const log = require('electron-log');
const { createWelcomeWindowStateManager } = require('./helpers/window_state.cjs');

const execAsync = promisify(exec);

class WelcomeScreen {
  constructor() {
    const isDev = process.env.NODE_ENV === 'development';

    // Create window state manager
    this.windowStateManager = createWelcomeWindowStateManager();

    // Get window options from state manager
    const windowOptions = this.windowStateManager.getWindowOptions();

    this.window = new BrowserWindow({
      ...windowOptions,
      minWidth: 500,
      minHeight: 400,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      },
      resizable: true,
      movable: true,
      show: false,
      backgroundColor: '#f5f5f5',
      title: 'Welcome to ClaraVerse'
    });

    // Restore window state (maximize, fullscreen)
    this.windowStateManager.restoreWindowState(this.window);

    // Log any errors
    this.window.webContents.on('crashed', (e) => {
      console.error('Welcome screen crashed:', e);
    });

    this.window.webContents.on('did-fail-load', (_event, code, description) => {
      console.error('Failed to load welcome screen:', code, description);
    });

    const htmlPath = isDev
      ? path.join(__dirname, 'welcome.html')
      : path.join(app.getAppPath(), 'electron', 'welcome.html');

    console.log('Loading welcome screen from:', htmlPath);
    this.window.loadFile(htmlPath);

    // Show window when ready
    this.window.once('ready-to-show', () => {
      const bounds = this.window.getBounds();
      log.info(`Welcome window ready to show with bounds:`, bounds);

      this.window.show();

      // Focus the window to ensure it's in front
      this.window.focus();

      // Setup window state handlers after window is shown
      setTimeout(() => {
        this.windowStateManager.setupEventHandlers(this.window);

        const finalBounds = this.window?.getBounds();
        log.info(`Welcome window final bounds after show:`, finalBounds);

        // Save initial state
        this.windowStateManager.saveCurrentState();
      }, 100);
    });
  }



  async checkContainerEngine() {
    try {
      // Check for Podman first
      try {
        await execAsync('command -v podman');
        await execAsync('podman --version');
        // Try to check if Podman is actually working (but don't fail if it's not running)
        try {
          await execAsync('podman info');
          return { available: true, engine: 'Podman', status: 'running' };
        } catch (infoError) {
          // Podman is installed but not running/configured
          return { available: true, engine: 'Podman', status: 'not_running', details: 'Podman is installed but may need to be started' };
        }
      } catch (podmanError) {
        // Check for Docker
        try {
          await execAsync('command -v docker');
          await execAsync('docker --version');
          // Try to check if Docker is actually working
          try {
            await execAsync('docker info');
            return { available: true, engine: 'Docker', status: 'running' };
          } catch (infoError) {
            // Docker is installed but not running
            return { available: true, engine: 'Docker', status: 'not_running', details: 'Docker is installed but not running' };
          }
        } catch (dockerError) {
          return {
            available: false,
            error: 'Neither Podman nor Docker found',
            details: 'Please install Podman (recommended) or Docker to continue'
          };
        }
      }
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }

  async checkNetwork() {
    try {
      // Try to reach a reliable endpoint
      await execAsync('curl -s --max-time 5 https://www.google.com > /dev/null');
      return { available: true };
    } catch (error) {
      return {
        available: false,
        error: 'No internet connection detected'
      };
    }
  }

  async checkStorage() {
    try {
      const homeDir = require('os').homedir();

      // Get disk usage (this is a simplified check)
      try {
        const { stdout } = await execAsync(`df -h "${homeDir}" | tail -1`);
        const parts = stdout.trim().split(/\s+/);
        const available = parts[3]; // Available space

        // Parse available space (rough check)
        const availableNum = parseFloat(available);
        const unit = available.slice(-1).toUpperCase();

        let availableGB = 0;
        if (unit === 'G') {
          availableGB = availableNum;
        } else if (unit === 'T') {
          availableGB = availableNum * 1024;
        } else if (unit === 'M') {
          availableGB = availableNum / 1024;
        }

        return {
          sufficient: availableGB > 2, // Need at least 2GB
          available: available,
          availableGB: availableGB
        };
      } catch (dfError) {
        // Fallback for systems without df command
        return {
          sufficient: true,
          available: 'Unknown',
          availableGB: 0
        };
      }
    } catch (error) {
      return {
        sufficient: true,
        available: 'Unknown',
        error: error.message
      };
    }
  }

  close() {
    if (this.window) {
      // Save final state before closing
      this.windowStateManager.saveCurrentState();
      this.window.close();
      this.window = null;
      this.windowStateManager = null;
    }
  }

  hide() {
    if (this.window) {
      this.window.hide();
    }
  }

  show() {
    if (this.window) {
      this.window.show();
    }
  }
}

module.exports = WelcomeScreen;
