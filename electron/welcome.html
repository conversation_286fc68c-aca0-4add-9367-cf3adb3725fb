<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Welcome to ClaraVerse</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      line-height: 1.6;
    }

    .container {
      background: rgba(255, 255, 255, 0.98);
      padding: 40px;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      text-align: center;
      max-width: 600px;
      width: 90%;
      position: relative;
      overflow: hidden;
      margin: 20px auto;
      min-height: calc(100vh - 40px);
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .logo {
      width: 120px;
      height: 120px;
      margin: 0 auto 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 48px;
      font-weight: bold;
    }

    h1 {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #2c3e50;
    }

    .subtitle {
      font-size: 18px;
      color: #7f8c8d;
      margin-bottom: 30px;
    }

    .status-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      margin: 20px 0;
      text-align: left;
    }

    .status-item {
      display: flex;
      align-items: center;
      margin: 12px 0;
      padding: 8px 0;
    }

    .status-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
    }

    .status-icon.checking {
      background: #f39c12;
      color: white;
      animation: pulse 1.5s infinite;
    }

    .status-icon.success {
      background: #27ae60;
      color: white;
    }

    .status-icon.error {
      background: #e74c3c;
      color: white;
    }

    .status-icon.warning {
      background: #f39c12;
      color: white;
    }

    .status-text {
      flex: 1;
    }

    .status-title {
      font-weight: 600;
      color: #2c3e50;
    }

    .status-detail {
      font-size: 14px;
      color: #7f8c8d;
      margin-top: 2px;
    }

    .actions {
      margin-top: 30px;
    }

    .btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin: 8px;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    .btn.secondary {
      background: #95a5a6;
    }

    .btn.success {
      background: #27ae60;
    }

    .btn:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .installation-guide {
      background: #e8f4fd;
      border: 1px solid #bee5eb;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      text-align: left;
    }

    .installation-guide h3 {
      color: #0c5460;
      margin-bottom: 10px;
    }

    .installation-guide p {
      color: #0c5460;
      margin-bottom: 8px;
    }

    .installation-guide code {
      background: #d1ecf1;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    .hidden {
      display: none;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Responsive design for smaller windows */
    @media (max-height: 600px) {
      .container {
        padding: 20px;
        margin: 10px auto;
        min-height: calc(100vh - 20px);
      }

      .logo {
        width: 80px;
        height: 80px;
        margin-bottom: 15px;
      }

      h1 {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .subtitle {
        font-size: 16px;
        margin-bottom: 20px;
      }

      .status-card {
        padding: 15px;
        margin: 15px 0;
      }

      .installation-guide {
        padding: 15px;
        margin: 15px 0;
      }
    }

    @media (max-width: 650px) {
      .container {
        width: 95%;
        padding: 20px;
      }

      .btn {
        padding: 10px 20px;
        font-size: 14px;
        margin: 6px;
      }

      .status-item {
        margin: 8px 0;
        padding: 6px 0;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">C</div>
    <h1>Welcome to ClaraVerse</h1>
    <p class="subtitle">AI-powered creativity and automation platform</p>

    <div class="status-card">
      <h3 style="margin-bottom: 15px; color: #2c3e50;">System Requirements</h3>

      <div class="status-item">
        <div class="status-icon checking" id="container-icon">?</div>
        <div class="status-text">
          <div class="status-title">Container Engine</div>
          <div class="status-detail" id="container-detail">Checking for Podman or Docker...</div>
        </div>
      </div>

      <div class="status-item">
        <div class="status-icon checking" id="network-icon">?</div>
        <div class="status-text">
          <div class="status-title">Network Access</div>
          <div class="status-detail" id="network-detail">Checking internet connectivity...</div>
        </div>
      </div>

      <div class="status-item">
        <div class="status-icon checking" id="storage-icon">?</div>
        <div class="status-text">
          <div class="status-title">Storage Space</div>
          <div class="status-detail" id="storage-detail">Checking available disk space...</div>
        </div>
      </div>
    </div>

    <div class="installation-guide hidden" id="installation-guide">
      <h3>🚀 Quick Setup</h3>
      <p><strong>Recommended:</strong> Install Podman (more secure, no root required)</p>
      <p id="install-command">• macOS: <code>brew install podman</code></p>
      <p>• Linux: <code>sudo apt install podman</code> or <code>sudo dnf install podman</code></p>
      <p>• Windows: Download from <a href="#" onclick="openExternal('https://podman-desktop.io/')">podman-desktop.io</a></p>
      <br>
      <p><strong>Alternative:</strong> Install Docker Desktop</p>
      <p>• Download from <a href="#" onclick="openExternal('https://www.docker.com/products/docker-desktop')">docker.com</a></p>
    </div>

    <div class="actions">
      <button class="btn hidden" id="continue-btn" onclick="continueSetup()">
        Continue Setup
      </button>
      <button class="btn secondary" id="retry-btn" onclick="retryCheck()" disabled>
        Retry Check <span class="spinner hidden" id="retry-spinner"></span>
      </button>
      <button class="btn secondary" onclick="openGuide()">
        Setup Guide
      </button>
      <button class="btn secondary" onclick="skipSetup()">
        Skip for Now
      </button>
    </div>

    <div style="margin-top: 20px; font-size: 14px; color: #7f8c8d;">
      <p>Need help? Check our <a href="#" onclick="openExternal('https://github.com/badboysm890/ClaraVerse/blob/main/PODMAN_SETUP.md')">setup guide</a></p>
    </div>
  </div>

  <script>
    const { ipcRenderer } = require('electron');

    let checkInProgress = false;

    async function checkPrerequisites() {
      if (checkInProgress) return;
      checkInProgress = true;

      const retryBtn = document.getElementById('retry-btn');
      const retrySpinner = document.getElementById('retry-spinner');
      retryBtn.disabled = true;
      retrySpinner.classList.remove('hidden');

      try {
        // Check container engine
        const containerResult = await ipcRenderer.invoke('check-container-engine');
        updateContainerStatus(containerResult);

        // Check network
        const networkResult = await ipcRenderer.invoke('check-network');
        updateNetworkStatus(networkResult);

        // Check storage
        const storageResult = await ipcRenderer.invoke('check-storage');
        updateStorageStatus(storageResult);

        // Update UI based on results
        updateActions(containerResult, networkResult, storageResult);

      } catch (error) {
        console.error('Error checking prerequisites:', error);
      } finally {
        checkInProgress = false;
        retryBtn.disabled = false;
        retrySpinner.classList.add('hidden');
      }
    }

    function updateContainerStatus(result) {
      const icon = document.getElementById('container-icon');
      const detail = document.getElementById('container-detail');

      if (result.available) {
        if (result.status === 'running') {
          icon.className = 'status-icon success';
          icon.textContent = '✓';
          detail.textContent = `${result.engine} is available and running`;
        } else {
          icon.className = 'status-icon warning';
          icon.textContent = '!';
          detail.textContent = `${result.engine} is installed but ${result.details || 'not running'}`;
        }
      } else {
        icon.className = 'status-icon error';
        icon.textContent = '✗';
        detail.textContent = result.details || 'No container engine found. Please install Podman or Docker.';
        document.getElementById('installation-guide').classList.remove('hidden');
      }
    }

    function updateNetworkStatus(result) {
      const icon = document.getElementById('network-icon');
      const detail = document.getElementById('network-detail');

      if (result.available) {
        icon.className = 'status-icon success';
        icon.textContent = '✓';
        detail.textContent = 'Internet connection available';
      } else {
        icon.className = 'status-icon warning';
        icon.textContent = '!';
        detail.textContent = 'Limited connectivity - some features may not work';
      }
    }

    function updateStorageStatus(result) {
      const icon = document.getElementById('storage-icon');
      const detail = document.getElementById('storage-detail');

      if (result.sufficient) {
        icon.className = 'status-icon success';
        icon.textContent = '✓';
        detail.textContent = `${result.available} available`;
      } else {
        icon.className = 'status-icon warning';
        icon.textContent = '!';
        detail.textContent = `Only ${result.available} available - may need more space`;
      }
    }

    function updateActions(container, network, storage) {
      const continueBtn = document.getElementById('continue-btn');

      if (container.available) {
        continueBtn.classList.remove('hidden');
        if (container.status === 'running') {
          continueBtn.className = 'btn success';
          continueBtn.textContent = 'Start ClaraVerse';
        } else {
          continueBtn.className = 'btn';
          continueBtn.textContent = `Start ClaraVerse (${container.engine} will be started)`;
        }
      }
    }

    function retryCheck() {
      checkPrerequisites();
    }

    function continueSetup() {
      ipcRenderer.invoke('start-main-app');
    }

    function skipSetup() {
      ipcRenderer.invoke('start-main-app-limited');
    }

    function openGuide() {
      ipcRenderer.invoke('open-setup-guide');
    }

    function openExternal(url) {
      ipcRenderer.invoke('open-external', url);
    }

    // Start checking prerequisites when page loads
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(checkPrerequisites, 500);
    });
  </script>
</body>
</html>
